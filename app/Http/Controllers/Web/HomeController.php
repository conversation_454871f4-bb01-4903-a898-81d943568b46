<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserInputSheet as UserInputSheetResource;
use App\Models\MstInputSample;
use App\Models\UserInputSheet;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Handle the incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function __invoke(Request $request)
    {
        $sample = MstInputSample::find(1);
        $histories = UserInputSheetResource::collection(UserInputSheet::where('user_id', $request->user()->id)->get());
        $user = $request->user();
        return [
            'success' => true,
            'sample' => $sample->account_json,
            'histories' => $histories,
            'user' => $user,
        ];
    }
}
