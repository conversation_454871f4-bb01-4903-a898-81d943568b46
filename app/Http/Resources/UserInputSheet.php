<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserInputSheet extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'userId' => $this->user_id,
            'groupingName' => $this->grouping_name,
            'dataName' => $this->data_name,
            'dataDate' => $this->data_date,
            'capitalizeDebtFromPresident' => boolval($this->capitalize_debt_from_president),
            'accountData' => json_decode($this->account_json),
            'filteredAccountData' => json_decode($this->filtered_account_json),
            'constitutionData' => json_decode($this->constitution_json),
            'disableChart' => boolval($this->disable_chart),
            'updatedDate' => $this->updated_at->toDateString(),
            'updatedTime' => $this->updated_at->toTimeString(),
        ];
    }
}
