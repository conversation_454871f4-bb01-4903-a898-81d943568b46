<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    protected $dates = ['start_at', 'end_at', 'last_login_at'];

    protected $with = ['theme'];

    /**
     * モデルの配列形態に追加するアクセサ
     *
     * @var array
     */
    protected $appends = ['listed_company_info_link'];

    public function theme()
    {
        return $this->BelongsTo('App\Models\MstTheme', 'mst_theme_id');
    }

    /**
     * 上場情報リンク設定アクセサ
     *
     * @return void
     */
    public function getListedCompanyInfoLinkAttribute()
    {
        return config('listedCompanyInfoLinks.link');
    }
}
