APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:PCxo2zUKzqJa6AmQMVW6Ij9AJE9Nx42lPZriFofgf8o=
APP_DEBUG=true
APP_URL=http://localhost
MIX_APP_REPOSITORY=api

WEB_PORT=8080

LOG_CHANNEL=stack

# DB_CONNECTION=mysql
# DB_HOST=db
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=
# DB_READ_USERNAME=
# DB_READ_PASSWORD=
# DB_WRITE_USERNAME=
# DB_WRITE_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=cookie
SESSION_LIFETIME=120

REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

ALLOW_IP_ADDRESS_WEB=
ALLOW_IP_ADDRESS_MANAGE=
