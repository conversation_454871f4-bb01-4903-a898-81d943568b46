<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateMstInputSamples extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mst_input_samples', function (Blueprint $table) {
            $table->integer('id')->primary()->comment('サンプルID');
            $table->json('account_json')->comment('科目JSON');
            $table->timestamps();
            $table->softDeletes();
        });
        DB::statement("ALTER TABLE users COMMENT '入力サンプル'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mst_input_samples');
    }
}
