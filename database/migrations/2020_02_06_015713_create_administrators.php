<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateAdministrators extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('administrators', function (Blueprint $table) {
            $table->increments('id')->comment('管理者ID');
            $table->string('name')->comment('管理者名');
            $table->string('account_id')->unique()->comment('アカウントID');
            $table->string('password')->comment('パスワード');
            $table->timestamps();
            $table->softDeletes();
        });
        DB::statement("ALTER TABLE users COMMENT '管理者情報'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('administrators');
    }
}
