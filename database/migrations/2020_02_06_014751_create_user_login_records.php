<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateUserLoginRecords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_login_records', function (Blueprint $table) {
            $table->increments('id')->comment('ログイン履歴ID');
            $table->integer('user_id')->unsigned()->comment('ユーザーID');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users');
        });
        DB::statement("ALTER TABLE users COMMENT 'ログイン履歴'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_login_records');
    }
}
