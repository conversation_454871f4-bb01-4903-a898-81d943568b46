<template>
  <div
    v-if="state"
    class="App"
  >
    <Menu
      ref="menu"
      :default-active="activeIndex"
      mode="horizontal"
      @select="handleSelectMenu"
    >
      <MenuItem index="input">
        新規入力
      </MenuItem>
      <MenuItem index="history">
        入力履歴
      </MenuItem>
      <li
        v-if="user.show_link == 1"
        class="menu-title"
      >
        <a
          :href="user.listed_company_info_link"
          target="_blank"
        >
          上場情報
        </a>
      </li>
      <MenuItem index="help">
        キャッシュ体質図解説
      </MenuItem>
      <li class="menu-title-button">
        <form
          ref="logout-form"
          action="/logout"
          method="post"
        >
          <input
            type="hidden"
            name="_token"
            :value="token"
          >
          <Button
            @click="handleLogout"
          >
            ログアウト
          </Button>
        </form>
      </li>
    </Menu>
    <div style="position: relative;">
      <PageHistory
        key="history"
        v-visible="activeIndex==='history'"
      />
      <PageInput
        v-show="activeIndex==='input' || activeIndex==='edit'"
        :key="current.id"
        style="position: absolute; top: -25px; left: 0; width: 100%;"
      />
      <PageHelp
        v-show="activeIndex==='help'"
        key="help"
        style="position: absolute; top: -25px; left: 0; width: 100%;"
      />
    </div>
  </div>
</template>

<script>
import { Menu, MenuItem, Button, Loading, MessageBox } from 'element-ui';
import PageInput from './PageInput';
import PageHistory from './PageHistory';
import PageHelp from './PageHelp';
import { mapState } from "vuex";

var updatetoken = true;

export default {
  components: { Menu, MenuItem, Button, PageInput, PageHistory, PageHelp },
  props: {
    token: { type: String, required: true }
  },
  data() {
    return {
      state: null,
    };
  },
  computed: {
    ...mapState('ui', { activeIndex: state => state.activeIndex }),
    ...mapState(["sample", "user", "histories", "current"]),
  },
  async created() {
    let loadingInstance = Loading.service({ fullscreen: true, text: 'Loading', lock: true });
    await this.$store.dispatch('init').finally(() => {
      this.$store.commit('csrfToken', this.token)
      this.state = 'init'
      loadingInstance.close();
    })
  },
  mounted() {
    const updateToken = () => {
      updatetoken = false;
      this.$store.dispatch('updateToken')
      .then(() => {
        setTimeout(() => {
          updatetoken = true
        }, 60000 * 5);
      })
      .catch(() => {
        MessageBox.alert('一定時間操作がなかったため自動的にログアウトしました。再度ログインしてください。', 'ログアウト', {
          center: true,
          type: 'warning',
        }).then(() => {
          Loading.service({ fullscreen: true, text: 'Loading', lock: true });
          window.location.reload(true);
        });
      });
    };
    document.onmousemove = function() {
      if (!updatetoken) return;
      updateToken();
    };
    document.onkeydown = function () {
      if (!updatetoken) return;
      updateToken();
    };
  },
  methods: {
    handleSelectMenu(key) {
      const change = () => {
        this.$store.commit('ui/activeIndex', key)
        this.$store.dispatch('current/init')
      };
      if (this.current.isDirty) {
        MessageBox.confirm('データが変更されています。<br>現在編集中のデータが破棄されますがよろしいですか。', '注意', {
          type: 'warning',
          center: true,
          dangerouslyUseHTMLString: true,
        }).then(() => {
          change();
        }).catch(() => {
          this.$refs['menu'].updateActiveIndex(this.activeIndex);
        })
      } else {
        change();
      }
    },
    handleLogout() {
      const logout = () => {
        Loading.service({ fullscreen: true, text: 'Loading', lock: true });
        this.$refs['logout-form'].submit();
      }
      if (this.current.isDirty) {
        MessageBox.confirm('データが変更されています。<br>現在編集中のデータが破棄されますがよろしいですか。', '注意', {
          type: 'warning',
          center: true,
          dangerouslyUseHTMLString: true,
        }).then(() => {
          logout();
        })
      } else {
        logout();
      }
    },
  }
};
</script>

<style lang="scss">
@import 'resources/sass/_mixin.scss';

.App {
  .el-menu {
    @include content-width
  }
  .menu-title, .menu-title-button {
    @include menu-item
  }
  .menu-title-button {
    float: right;
  }
}
</style>
