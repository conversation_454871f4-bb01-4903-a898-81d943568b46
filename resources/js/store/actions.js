import { RepositoryFactory } from '../repositories/RepositoryFactory'

const Repository = RepositoryFactory.get(process.env.MIX_APP_REPOSITORY)

export default {
  init: async (context, payload = {}) => {
    const response = await repositoryAccess('init', context, payload)
    context.commit("init", response.data);
    context.dispatch("current/init");
  },
  copyInputSheet: (context, payload = {}) => {
    context.dispatch("current/copy", payload)
    context.commit('ui/activeIndex', 'input')
  },
  editInputSheet: (context, payload = {}) => {
    context.dispatch("current/edit", payload)
    context.commit('ui/activeIndex', 'edit')
  },
  storeInputSheet: async (context) => {
    const payload = context.getters['current/postParam']
    await repositoryAccess('storeSheet', context, payload)
    .then(res => {
      context.commit('pushInputSheet', res.data.userInputSheet)
      context.dispatch('current/init')
      context.commit('ui/setSelectedGroup', res.data.userInputSheet.groupingName)
      context.commit('ui/activeIndex', 'history')
    })
  },
  updateInputSheet: async (context) => {
    const payload = {
      id: context.state.current.id,
      postParam: context.getters['current/postParam'],
    }
    await repositoryAccess('updateSheet', context, payload)
    .then(res => {
      context.commit('updateInputSheet', res.data.userInputSheet)
      context.dispatch('current/init')
      context.commit('ui/setSelectedGroup', res.data.userInputSheet.groupingName)
      context.commit('ui/activeIndex', 'history')
    })
  },
  deleteInputSheet: async (context, payload = {}) => {
    await repositoryAccess('deleteSheet', context, payload)
    .then(() => {
      context.commit("deleteInputSheet", payload);
    })
  },
  initManage: async (context, payload = {}) => {
    context.commit('ui/activeIndex', 'history')
    const response = await repositoryAccess('initManage', context, payload)
    context.commit("init", response.data);
  },
  showManage: (context, payload = {}) => {
    context.dispatch("current/edit", payload)
    context.commit('ui/activeIndex', 'sheet')
  },
  updateToken: async (context, payload = {}) => {
    await repositoryAccess('updateToken', context, payload)
  }
}

export const repositoryAccess = (repositoryFunction, context, payload) => {
  return new Promise((resolve, reject) => {
    const config = {
      headers: {
      }
    };
    Repository[repositoryFunction](payload, config)
      .then(response => {
        console.debug(response)
        resolve(response)
      })
      .catch(error => {
        console.debug(error)
        reject(new Error())
      })
  })
}
