// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
    "name": "TGM Dev Container",
    "dockerComposeFile": [
        "./compose.yml"
    ],
    "service": "app",
    "containerUser": "vscode",
    "workspaceFolder": "/var/www/html",
    "features": {
        "ghcr.io/devcontainers/features/github-cli:1": {},
        "ghcr.io/devcontainers/features/git:1": {},
        "ghcr.io/devcontainers-extra/features/fd:1": {},
        "ghcr.io/devcontainers-extra/features/ripgrep:1": {},
        "ghcr.io/dhoeric/features/hadolint:1": {},
        "ghcr.io/shyim/devcontainers-features/php:0": {
            "version": "7.2",
            "extensionsExtra": "xdebug,gd,mbstring"
        },
        "ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {
            "packages": "libxext6,fontconfig,libxrender1,xfonts-75dpi,xfonts-base"
        },
        "ghcr.io/devcontainers/features/node:1": {
            "version": "10.24.1"
        }
    },
    "remoteUser": "vscode",
    "overrideCommand": true,
    // "postAttachCommand": "for dir in vendor node_modules; do if [ -d \"$dir\" ]; then owner=$(stat -c '%U' \"$dir\"); group=$(stat -c '%G' \"$dir\"); if [ \"$owner\" != \"vscode\" ] || [ \"$group\" != \"vscode\" ]; then sudo chown -R vscode:vscode \"$dir\"; fi; fi; done",
    "customizations": {
        "vscode": {
            "extensions": [
                "editorconfig.editorconfig", // エディタを跨いだ共通設定ファイルをVsCodeで実行するのに必要
                "ms-azuretools.vscode-docker", // VsCodeでのIntelliSenseやタブ補完用
                "xdebug.php-debug", // デバッガ
                "bmewburn.vscode-intelephense-client", // PHP インテリセンス
                "onecentlin.laravel-extension-pack", // Laravel用拡張機能群 https://zenn.dev/na9/articles/23c18a0d2d8ee2#12%E5%80%8B%E3%81%AE%E6%8B%A1%E5%BC%B5%E6%A9%9F%E8%83%BD%E3%81%9F%E3%81%A1
                "spywhere.guides", // インデントを見やすくする
                "streetsidesoftware.code-spell-checker", // タイポチェック
                "mosapride.zenkaku", // 全角の強調表示
                "mhutchie.git-graph", // git
                "donjayamanne.githistory", // git
                "Augment.vscode-augment", // Augment
                "exiasr.hadolint", // Dockerfile
                "Vue.volar" // Vue
            ]
        }
    }
}
