{
    // vim: ft=jsonc
    // # devcontainer.json sample
    // recommend: Do not sort json
    // ## To create image
    "name": "TGM Dev Container",
    "workspaceFolder": "/var/www/html",
    "shutdownAction": "stopCompose",
    // ## From base image
    "dockerComposeFile": [
        "../compose.yml"
    ],
    "service": "app",
    "runServices": [],
    // ## Resources
    // warning: Can not use Env
    // "mounts": [
    //     {
    //         "type": "bind",
    //         "source": "${localWorkspaceFolder}",
    //         "target": "${containerWorkspaceFolder}",
    //         "consistency": "delegated"
    //     }
    // ],
    // "features": {
    //     "ghcr.io/devcontainers/features/common-utils:2": {
    //         "username": "developer"
    //     },
    //     "ghcr.io/devcontainers/features/git:1": {}
    // },
    // ## Environment
    "remoteUser": "smatsui",
    "containerEnv": {},
    "remoteEnv": {},
    // "portsAttributes": {
    //     "80": {
    //         "label": "http",
    //         "onAutoForward": "silent"
    //     }
    // },
    // ## Container command
    // warning: To use .sh you need mount
    // info: key is output stage
    "overrideCommand": true,
    // ## IDE
    "customizations": {
        "vscode": {
            "extensions": [
                "editorconfig.editorconfig", // エディタを跨いだ共通設定ファイルをVsCodeで実行するのに必要
                "ms-azuretools.vscode-docker", // VsCodeでのIntelliSenseやタブ補完用
                "xdebug.php-debug", // デバッガ
                "bmewburn.vscode-intelephense-client", // PHP インテリセンス
                "onecentlin.laravel-extension-pack", // Laravel用拡張機能群 https://zenn.dev/na9/articles/23c18a0d2d8ee2#12%E5%80%8B%E3%81%AE%E6%8B%A1%E5%BC%B5%E6%A9%9F%E8%83%BD%E3%81%9F%E3%81%A1
                "spywhere.guides", // インデントを見やすくする
                "streetsidesoftware.code-spell-checker", // タイポチェック
                "mosapride.zenkaku", // 全角の強調表示
                "mhutchie.git-graph", // git
                "donjayamanne.githistory" // git
            ],
            "settings": {}
        }
    }
}
