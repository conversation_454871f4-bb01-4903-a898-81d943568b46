services:
  app:
    build:
      context: .
      dockerfile: ./docker/php/Dockerfile.apache
    ports:
      - "${WEB_PORT:-8080}:80"
    depends_on:
      - db
    environment:
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=${DB_PORT:-3306}
      - DB_DATABASE=${DB_DATABASE:-tgm}
      - DB_USERNAME=${DB_USERNAME:-tgm_user}
      - DB_PASSWORD=${DB_PASSWORD:-tgm_password}
      - DB_READ_USERNAME=${DB_READ_USERNAME:-tgm_user}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD:-tgm_password}
      - DB_WRITE_USERNAME=${DB_WRITE_USERNAME:-tgm_user}
      - DB_WRITE_PASSWORD=${DB_WRITE_PASSWORD:-tgm_password}
      - TZ=Asia/Tokyo

  db:
    image: mysql:8.0
    ports:
      - "3306"
    environment:
      - MYSQL_DATABASE=${DB_DATABASE:-tgm}
      - MYSQL_USER=${DB_USERNAME:-tgm_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-tgm_password}
      - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD:-root_password}
      - TZ=Asia/Tokyo
    volumes:
      - vol_mysql:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf

volumes:
  vol_mysql:
    driver: local
