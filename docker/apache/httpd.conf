ServerRoot "/usr/local/apache2"
Listen 80

LoadModule mpm_event_module modules/mod_mpm_event.so
LoadModule dir_module modules/mod_dir.so
LoadModule proxy_module modules/mod_proxy.so
LoadModule proxy_fcgi_module modules/mod_proxy_fcgi.so
LoadModule rewrite_module modules/mod_rewrite.so
LoadModule authz_core_module modules/mod_authz_core.so
LoadModule unixd_module modules/mod_unixd.so
LoadModule log_config_module modules/mod_log_config.so

ServerName localhost
User daemon
Group daemon

DocumentRoot "/var/www/html/public"
<Directory /var/www/html/public>
  AllowOverride All
  Require all granted
</Directory>

ProxyPassMatch "^/(.*\.php(/.*)?)$" "fcgi://app:9000/var/www/html/public/$1"

DirectoryIndex index.php index.html

ErrorLog /dev/stdout
CustomLog /dev/stdout combined
