FROM ubuntu:22.04

# Set PHP and Node.js versions as ARGs
ARG PHP_VERSION=7.2
ARG NODE_VERSION=10.24.1

# Set PHP version as an environment variable
ENV PHP_VERSION=${PHP_VERSION}

# Avoid prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Update and install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    software-properties-common \
    ca-certificates \
    gpg \
    gnupg \
    curl \
    git \
    unzip \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libxml2-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Add PHP repository and install PHP
RUN add-apt-repository ppa:ondrej/php && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    php${PHP_VERSION} \
    php${PHP_VERSION}-cli \
    php${PHP_VERSION}-fpm \
    php${PHP_VERSION}-common \
    php${PHP_VERSION}-mysql \
    php${PHP_VERSION}-zip \
    php${PHP_VERSION}-gd \
    php${PHP_VERSION}-mbstring \
    php${PHP_VERSION}-curl \
    php${PHP_VERSION}-xml \
    php${PHP_VERSION}-bcmath \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set up mise environment variables
ENV MISE_DATA_DIR="/mise"
ENV MISE_CONFIG_DIR="/mise"
ENV MISE_CACHE_DIR="/mise/cache"
ENV MISE_INSTALL_PATH="/usr/local/bin/mise"
ENV PATH="/mise/shims:$PATH"

# Set shell options for pipefail
SHELL ["/bin/bash", "-o", "pipefail", "-c"]

# Install mise and Node.js with specified version
RUN curl https://mise.run | sh \
    && /usr/local/bin/mise install node@${NODE_VERSION} \
    && /usr/local/bin/mise trust

# Set working directory
WORKDIR /var/www/html

# Create an ENV from the ARG for use at runtime
ENV PHP_VERSION=${PHP_VERSION}

# Default command
CMD ["sh", "-c", "php-fpm${PHP_VERSION}"]
