ARG PHP_VERSION=7.2.34
FROM php:${PHP_VERSION}-apache

ARG NODE_VERSION=10.24.1

# Install dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    unzip \
    git \
    curl \
    fontconfig \
    libxrender1 \
    xfonts-75dpi \
    xfonts-base \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# PHP extensions
RUN docker-php-ext-configure gd --with-freetype-dir=/usr/include/ --with-jpeg-dir=/usr/include/ \
    && docker-php-ext-install -j"$(nproc)" \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    zip \
    xml

# wkhtmltopdf
# ADD https://github.com/wkhtmltopdf/wkhtmltopdf/releases/download/0.12.5/wkhtmltox_0.12.5-1.stretch_amd64.deb .
# RUN dpkg -i wkhtmltox_0.12.5-1.stretch_amd64.deb || true \
#     && apt-get update && apt-get install -f -y \
#     && dpkg -i wkhtmltox_0.12.5-1.stretch_amd64.deb \
#     && rm wkhtmltox_0.12.5-1.stretch_amd64.deb \
#     && apt-get clean \
#     && rm -rf /var/lib/apt/lists/*

ARG NODE_VERSION=10.24.1
# Install mise and Node.js
SHELL ["/bin/bash", "-o", "pipefail", "-c"]
ENV MISE_DATA_DIR="/mise"
ENV MISE_CONFIG_DIR="/mise"
ENV MISE_CACHE_DIR="/mise/cache"
ENV MISE_INSTALL_PATH="/usr/local/bin/mise"
ENV PATH="/mise/shims:$PATH"
RUN curl https://mise.run | sh \
    && /usr/local/bin/mise install "node@${NODE_VERSION}" \
    && /usr/local/bin/mise use --global "node@${NODE_VERSION}"

# Copy composer from composer image
COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy application code
COPY . .

# Install composer dependencies
RUN composer install --no-dev --optimize-autoloader --no-interaction

# Install npm dependencies and build assets
RUN npm ci && npm run prod

# Set the document root to Laravel's public folder
ENV APACHE_DOCUMENT_ROOT /var/www/html/public
RUN sed -ri -e "s!/var/www/html!${APACHE_DOCUMENT_ROOT}!g" /etc/apache2/sites-available/*.conf \
    && sed -ri -e "s!/var/www/!${APACHE_DOCUMENT_ROOT}!g" /etc/apache2/apache2.conf /etc/apache2/conf-available/*.conf
RUN sed -i '/<Directory \/var\/www\/html>/,/<\/Directory>/ s/AllowOverride None/AllowOverride All/' /etc/apache2/apache2.conf \
    && if ! grep -q "<Directory /var/www/html/public>" /etc/apache2/apache2.conf; then echo -e "<Directory /var/www/html/public>\n\tAllowOverride All\n</Directory>" >> /etc/apache2/apache2.conf; fi

# Copy application code (excluding vendor and node_modules)
COPY . /var/www/html

# Setup .env
RUN cp .env.production .env

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage /var/www/html/bootstrap/cache

# Use the default production configuration
RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini"

# Copy custom PHP configuration
COPY ./docker/php/php.ini "$PHP_INI_DIR/conf.d/custom.ini"

# Copy entrypoint script
COPY ./docker/php/docker-entrypoint.sh.production /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/docker-entrypoint"]

# Start Apache
CMD ["apache2-foreground"]
